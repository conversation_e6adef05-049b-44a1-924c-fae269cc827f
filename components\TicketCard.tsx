import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ticket } from '@/lib/mockData';
import { QrCode, Crown, User } from 'lucide-react-native';

interface TicketCardProps {
  ticket: Ticket;
  onPress?: () => void;
}

export default function TicketCard({ ticket, onPress }: TicketCardProps) {
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'vip': return '#8B5CF6';
      case 'backstage': return '#EF4444';
      case 'press': return '#F59E0B';
      default: return '#06B6D4';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'vip': return <Crown size={16} color="#FFFFFF" />;
      case 'backstage': return <User size={16} color="#FFFFFF" />;
      case 'press': return <User size={16} color="#FFFFFF" />;
      default: return <QrCode size={16} color="#FFFFFF" />;
    }
  };

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.header}>
        <View style={styles.ticketInfo}>
          <Text style={styles.holderName}>{ticket.holder_name}</Text>
          <Text style={styles.ticketCode}>{ticket.ticket_code}</Text>
        </View>
        
        <View style={[styles.categoryBadge, { backgroundColor: getCategoryColor(ticket.category) }]}>
          {getCategoryIcon(ticket.category)}
          <Text style={styles.categoryText}>
            {ticket.category.toUpperCase()}
          </Text>
        </View>
      </View>

      <View style={styles.footer}>
        <Text style={styles.email}>{ticket.holder_email}</Text>
        <View style={[styles.statusBadge, ticket.is_used ? styles.usedBadge : styles.validBadge]}>
          <Text style={styles.statusText}>
            {ticket.is_used ? 'KULLANILDI' : 'GEÇERLİ'}
          </Text>
        </View>
      </View>

      {ticket.is_used && ticket.used_at && (
        <Text style={styles.usedDate}>
          Kullanıldı: {new Date(ticket.used_at).toLocaleString('tr-TR')}
        </Text>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  ticketInfo: {
    flex: 1,
  },
  holderName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  ticketCode: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  categoryBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    gap: 4,
  },
  categoryText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  email: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  validBadge: {
    backgroundColor: '#D1FAE5',
  },
  usedBadge: {
    backgroundColor: '#FEE2E2',
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#065F46',
  },
  usedDate: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    marginTop: 8,
  },
});