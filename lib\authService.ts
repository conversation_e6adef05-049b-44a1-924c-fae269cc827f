import AsyncStorage from '@react-native-async-storage/async-storage';
import { mockUsers, User } from './mockData';

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
}

class AuthService {
  private static instance: AuthService;
  private currentUser: User | null = null;

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  async signIn(email: string, password: string): Promise<{ user?: User; error?: string }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Demo credentials
    const validCredentials = [
      { email: '<EMAIL>', password: 'admin123' },
      { email: '<EMAIL>', password: 'scanner123' }
    ];

    const isValid = validCredentials.some(
      cred => cred.email === email && cred.password === password
    );

    if (!isValid) {
      return { error: 'Geçersiz email veya şifre' };
    }

    const user = mockUsers.find(u => u.email === email);
    if (!user) {
      return { error: 'Kullanıcı bulunamadı' };
    }

    this.currentUser = user;
    await AsyncStorage.setItem('currentUser', JSON.stringify(user));
    
    return { user };
  }

  async signOut(): Promise<void> {
    this.currentUser = null;
    await AsyncStorage.removeItem('currentUser');
  }

  async getCurrentUser(): Promise<User | null> {
    if (this.currentUser) {
      return this.currentUser;
    }

    try {
      const userData = await AsyncStorage.getItem('currentUser');
      if (userData) {
        this.currentUser = JSON.parse(userData);
        return this.currentUser;
      }
    } catch (error) {
      console.error('Error getting current user:', error);
    }

    return null;
  }

  async isAuthenticated(): Promise<boolean> {
    const user = await this.getCurrentUser();
    return user !== null;
  }
}

export default AuthService.getInstance();