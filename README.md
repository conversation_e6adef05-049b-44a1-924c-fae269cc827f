# BiletTarayıcı - Etkinlik Bilet Doğrulama Sistemi

QR kod ve barkod kullanarak etkinlik biletlerini tarayan ve doğrulayan kapsamlı mobil uygulama.

## Özellikler

### Temel Fonksiyonlar
- **QR/Barkod Tarama**: Gerçek zamanlı kamera tabanlı bilet tarama
- **Online/Offline Doğrulama**: İnternet bağlantısı olan veya olmayan ortamlarda çalışır
- **Görsel & Sesli Geri Bildirim**: Ses uyarıları ile net yeşil/kırmızı ekranlar
- **Rol Tabanlı Erişim**: Farklı izinlere sahip Yönetici ve Tarayıcı kullanıcı rolleri

### Etkinlik Yönetimi
- Mekan ve tarih bilgileri ile etkinlik oluşturma ve yönetme
- Tarayıcı personeli belirli etkinliklere atama
- Bilet kategorilerini takip etme (Normal, VIP, Backstage, Basın)

### Güvenlik & Doğrulama
- Çift tarama algılama ve kayıt tutma
- Tüm tarama faaliyetlerinin kapsamlı denetim izi
- Veri koruması için güvenlik politikaları

### Raporlama & Analitik
- Gerçek zamanlı etkinlik istatistikleri ve ilerleme takibi
- Kategoriye dayalı tarama raporları
- Güvenlik için çift giriş denemesi izleme

### Offline Yetenekler
- Offline tarama için bilet verilerini indirme
- İnternet mevcut olmadığında taramaları yerel olarak saklama
- Bağlantı geri geldiğinde otomatik senkronizasyon

## Teknoloji Yığını

- **Frontend**: React Native with Expo
- **Veri Yönetimi**: Mock data with AsyncStorage
- **Kimlik Doğrulama**: Custom auth service
- **Kamera**: Expo Camera with barcode scanning
- **Offline Depolama**: AsyncStorage for local data

## Demo Hesaplar

- **Yönetici**: <EMAIL> / admin123
- **Tarayıcı**: <EMAIL> / scanner123

## Veri Yapısı

### Kullanıcılar & Profiller
- Rol tabanlı kimlik doğrulama (admin/scanner)
- Tarayıcı kullanıcılar için etkinlik atamaları

### Etkinlikler
- Mekan ve zamanlama ile etkinlik yönetimi
- Aktif/pasif durum kontrolleri

### Biletler
- QR/barkod desteği ile benzersiz bilet kodları
- Özel erişim seviyelerine sahip çoklu kategoriler
- Kullanım takibi ve doğrulama durumu

### Denetim İzi
- Tam tarama geçmişi
- Çift giriş denemesi kayıtları
- Performans analitiği

## Kurulum Talimatları

1. **Geliştirme**:
   ```bash
   npm run dev
   ```

2. **Mock Veri**: 
   - Uygulama önceden tanımlanmış demo verilerle gelir
   - Tüm veriler `lib/mockData.ts` dosyasında bulunur
   - AsyncStorage kullanarak yerel depolama

## Güvenlik Özellikleri

- Rol tabanlı veri erişim kontrolleri
- Uygun kimlik doğrulama ile güvenli API uç noktaları
- Çift tarama algılama ve kayıt tutma
- Güvenli yerel veri depolama

## Offline Destek

Uygulama kapsamlı offline işlevsellik içerir:

1. **İndirme Modu**: Etkinliklerden önce bilet verilerini önceden indirme
2. **Offline Tarama**: İnternet olmadan biletleri doğrulama
3. **Senkronizasyon Modu**: Bağlantı geri geldiğinde offline taramaları yükleme
4. **Depolama Yönetimi**: Verimli yerel veri depolama

Bu, bağlantısı zayıf mekanlarda bile kesintisiz çalışma sağlar.

## Backend Entegrasyonu İçin Hazır

Sistem, gerçek backend API'sine kolayca bağlanabilecek şekilde tasarlanmıştır:

- `lib/authService.ts` - Kimlik doğrulama servisi
- `lib/ticketService.ts` - Bilet işlemleri servisi
- `lib/mockData.ts` - Veri yapıları ve mock veriler

Mock servisler gerçek API çağrıları ile değiştirilebilir.