import AsyncStorage from '@react-native-async-storage/async-storage';
import { mockTickets, mockScans, mockEvents, Ticket, Scan, EventStats } from './mockData';
import authService from './authService';

export class TicketService {
  // Simulate network delay
  private static async simulateDelay(ms: number = 500): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, ms));
  }

  // Online ticket validation
  static async validateTicketOnline(ticketCode: string, eventId: string): Promise<{
    isValid: boolean;
    ticket?: Ticket;
    message: string;
    isUsed?: boolean;
  }> {
    try {
      await this.simulateDelay();
      
      const ticket = mockTickets.find(
        t => t.ticket_code === ticketCode && t.event_id === eventId
      );

      if (!ticket) {
        return { isValid: false, message: 'Bilet bulunamadı' };
      }

      if (ticket.is_used) {
        return { 
          isValid: false, 
          ticket, 
          message: '<PERSON><PERSON><PERSON> da<PERSON>',
          isUsed: true 
        };
      }

      return { isValid: true, ticket, message: 'Geçerli bilet' };
    } catch (error) {
      return { isValid: false, message: 'A<PERSON> hatası' };
    }
  }

  // Offline ticket validation
  static async validateTicketOffline(ticketCode: string, eventId: string): Promise<{
    isValid: boolean;
    ticket?: Ticket;
    message: string;
    isUsed?: boolean;
  }> {
    try {
      const offlineTickets = await AsyncStorage.getItem(`offline_tickets_${eventId}`);
      const usedTickets = await AsyncStorage.getItem(`used_tickets_${eventId}`);
      
      if (!offlineTickets) {
        return { isValid: false, message: 'Offline veri mevcut değil' };
      }

      const tickets: Ticket[] = JSON.parse(offlineTickets);
      const used: string[] = usedTickets ? JSON.parse(usedTickets) : [];

      const ticket = tickets.find(t => t.ticket_code === ticketCode);
      
      if (!ticket) {
        return { isValid: false, message: 'Bilet bulunamadı' };
      }

      if (used.includes(ticketCode)) {
        return { 
          isValid: false, 
          ticket, 
          message: 'Bilet daha önce kullanılmış',
          isUsed: true 
        };
      }

      return { isValid: true, ticket, message: 'Geçerli bilet' };
    } catch (error) {
      return { isValid: false, message: 'Offline doğrulama hatası' };
    }
  }

  // Mark ticket as used
  static async markTicketUsed(ticketCode: string, eventId: string, isOffline = false): Promise<boolean> {
    try {
      if (isOffline) {
        // Store offline usage
        const usedTickets = await AsyncStorage.getItem(`used_tickets_${eventId}`);
        const used: string[] = usedTickets ? JSON.parse(usedTickets) : [];
        used.push(ticketCode);
        await AsyncStorage.setItem(`used_tickets_${eventId}`, JSON.stringify(used));

        // Store pending sync
        const pendingScans = await AsyncStorage.getItem('pending_scans');
        const pending = pendingScans ? JSON.parse(pendingScans) : [];
        pending.push({
          ticketCode,
          eventId,
          timestamp: new Date().toISOString(),
        });
        await AsyncStorage.setItem('pending_scans', JSON.stringify(pending));

        return true;
      } else {
        // Update mock data
        await this.simulateDelay();
        const user = await authService.getCurrentUser();
        if (!user) return false;

        // Find and update ticket in mock data
        const ticketIndex = mockTickets.findIndex(t => t.ticket_code === ticketCode);
        if (ticketIndex === -1) return false;

        mockTickets[ticketIndex] = {
          ...mockTickets[ticketIndex],
          is_used: true,
          used_at: new Date().toISOString(),
          used_by: user.id
        };

        // Add scan record
        mockScans.push({
          id: `scan-${Date.now()}`,
          ticket_id: ticketCode,
          scanned_by: user.id,
          scanned_at: new Date().toISOString(),
          is_duplicate: false,
          event_id: eventId
        });

        return true;
      }
    } catch (error) {
      return false;
    }
  }

  // Download tickets for offline use
  static async downloadTicketsForEvent(eventId: string): Promise<boolean> {
    try {
      await this.simulateDelay();
      const tickets = mockTickets.filter(t => t.event_id === eventId);

      await AsyncStorage.setItem(`offline_tickets_${eventId}`, JSON.stringify(tickets));
      await AsyncStorage.setItem(`last_sync_${eventId}`, new Date().toISOString());
      
      return true;
    } catch (error) {
      return false;
    }
  }

  // Sync offline scans when online
  static async syncOfflineScans(): Promise<void> {
    try {
      const pendingScans = await AsyncStorage.getItem('pending_scans');
      if (!pendingScans) return;

      const scans = JSON.parse(pendingScans);
      const user = await authService.getCurrentUser();
      if (!user) return;

      for (const scan of scans) {
        // Update mock ticket
        const ticketIndex = mockTickets.findIndex(t => t.ticket_code === scan.ticketCode);
        if (ticketIndex !== -1) {
          mockTickets[ticketIndex] = {
            ...mockTickets[ticketIndex],
            is_used: true,
            used_at: scan.timestamp,
            used_by: user.id
          };
        }

        // Add to mock scans
        mockScans.push({
          id: `sync-scan-${Date.now()}-${Math.random()}`,
          ticket_id: scan.ticketCode,
          scanned_by: user.id,
          scanned_at: scan.timestamp,
          is_duplicate: false,
          event_id: scan.eventId
        });
      }

      await AsyncStorage.removeItem('pending_scans');
    } catch (error) {
      console.error('Sync error:', error);
    }
  }

  // Get event statistics
  static async getEventStats(eventId: string): Promise<EventStats | null> {
    try {
      await this.simulateDelay();
      
      const tickets = mockTickets.filter(t => t.event_id === eventId);
      const scans = mockScans.filter(s => s.event_id === eventId);

      const total_tickets = tickets.length;
      const scanned_tickets = tickets.filter(t => t.is_used).length;
      const pending_tickets = total_tickets - scanned_tickets;
      const duplicate_attempts = scans.filter(s => s.is_duplicate).length;
      const vip_scanned = tickets.filter(t => t.is_used && t.category === 'vip').length;
      const regular_scanned = tickets.filter(t => t.is_used && t.category === 'regular').length;
      const backstage_scanned = tickets.filter(t => t.is_used && t.category === 'backstage').length;
      const press_scanned = tickets.filter(t => t.is_used && t.category === 'press').length;

      return {
        total_tickets,
        scanned_tickets,
        pending_tickets,
        duplicate_attempts,
        vip_scanned,
        regular_scanned,
        backstage_scanned,
        press_scanned,
      };
    } catch (error) {
      return null;
    }
  }

  // Get all events
  static async getAllEvents(): Promise<Event[]> {
    await this.simulateDelay();
    return mockEvents;
  }

  // Get tickets for event
  static async getTicketsForEvent(eventId: string): Promise<Ticket[]> {
    await this.simulateDelay();
    return mockTickets.filter(t => t.event_id === eventId);
  }

  // Create new event (mock)
  static async createEvent(eventData: Omit<Event, 'id' | 'created_at'>): Promise<Event> {
    await this.simulateDelay();
    
    const newEvent: Event = {
      ...eventData,
      id: `event-${Date.now()}`,
      created_at: new Date().toISOString()
    };
    
    mockEvents.push(newEvent);
    return newEvent;
  }
}