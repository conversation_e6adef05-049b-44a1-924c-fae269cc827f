import { Tabs, Redirect } from 'expo-router';
import { QrCode, Users, ChartBar as BarChart3, Settings } from 'lucide-react-native';
import { useAuth } from '@/contexts/AuthContext';
import { ActivityIndicator, View } from 'react-native';

export default function TabLayout() {
  const { user, userRole, loading } = useAuth();

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#3B82F6" />
      </View>
    );
  }

  if (!user) {
    return <Redirect href="/(auth)/login" />;
  }

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: '#3B82F6',
        tabBarInactiveTintColor: '#6B7280',
        tabBarStyle: {
          backgroundColor: '#FFFFFF',
          borderTopColor: '#E5E7EB',
          paddingTop: 8,
          paddingBottom: 8,
          height: 72,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontFamily: 'Inter-SemiBold',
          marginTop: 4,
        },
      }}
    >
      <Tabs.Screen
        name="scanner"
        options={{
          title: 'Tarayıcı',
          tabBarIcon: ({ size, color }) => <QrCode size={size} color={color} />,
        }}
      />
      
      {userRole === 'admin' && (
        <>
          <Tabs.Screen
            name="events"
            options={{
              title: 'Etkinlikler',
              tabBarIcon: ({ size, color }) => <Users size={size} color={color} />,
            }}
          />
          <Tabs.Screen
            name="reports"
            options={{
              title: 'Raporlar',
              tabBarIcon: ({ size, color }) => <BarChart3 size={size} color={color} />,
            }}
          />
        </>
      )}
      
      <Tabs.Screen
        name="settings"
        options={{
          title: 'Ayarlar',
          tabBarIcon: ({ size, color }) => <Settings size={size} color={color} />,
        }}
      />
    </Tabs>
  );
}