import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Vibration, SafeAreaView } from 'react-native';
import { CameraView, useCameraPermissions } from 'expo-camera';
import { Audio } from 'expo-av';
import { TicketService } from '@/lib/ticketService';
import { useAuth } from '@/contexts/AuthContext';
import { Ticket } from '@/lib/mockData';
import { QrCode, Wifi, WifiOff } from 'lucide-react-native';

export default function ScannerScreen() {
  const [permission, requestPermission] = useCameraPermissions();
  const [scanning, setScanning] = useState(true);
  const [isOnline, setIsOnline] = useState(true);
  const [lastScanResult, setLastScanResult] = useState<{
    isValid: boolean;
    ticket?: Ticket;
    message: string;
    isUsed?: boolean;
  } | null>(null);
  const [selectedEventId, setSelectedEventId] = useState('demo-event-1');
  const [selectedEventId, setSelectedEventId] = useState('event-1');
  const { user } = useAuth();

  useEffect(() => {
    // Check network connectivity periodically
    const checkConnection = async () => {
      try {
        const response = await fetch('https://httpbin.org/get', { 
          method: 'HEAD',
          signal: AbortSignal.timeout(3000) 
        });
        setIsOnline(response.ok);
      } catch {
        setIsOnline(false);
      }
    };

    checkConnection();
    const interval = setInterval(checkConnection, 10000);
    return () => clearInterval(interval);
  }, []);

  const playSound = async (type: 'success' | 'error') => {
    try {
      const { sound } = await Audio.Sound.createAsync(
        type === 'success' 
          ? require('@/assets/sounds/success.mp3')
          : require('@/assets/sounds/error.mp3')
      );
      await sound.playAsync();
    } catch (error) {
      // Fallback to system sounds or vibration
      Vibration.vibrate(type === 'success' ? 100 : [100, 100, 100]);
    }
  };

  const handleBarcodeScanned = async ({ data }: { data: string }) => {
    if (!scanning) return;
    
    setScanning(false);
    
    try {
      const result = isOnline 
        ? await TicketService.validateTicketOnline(data, selectedEventId)
        : await TicketService.validateTicketOffline(data, selectedEventId);

      setLastScanResult(result);

      if (result.isValid) {
        await TicketService.markTicketUsed(data, selectedEventId, !isOnline);
        await playSound('success');
        Vibration.vibrate(100);
      } else {
        await playSound('error');
        Vibration.vibrate([100, 100, 100]);
      }

      // Show result for 3 seconds, then resume scanning
      setTimeout(() => {
        setLastScanResult(null);
        setScanning(true);
      }, 3000);

    } catch (error) {
      setLastScanResult({
        isValid: false,
        message: 'Scanning error occurred'
      });
      setTimeout(() => {
        setLastScanResult(null);
        setScanning(true);
      }, 3000);
    }
  };

  if (!permission) {
    return (
      <View style={styles.permissionContainer}>
        <Text>Kamera izni isteniyor...</Text>
      </View>
    );
  }

  if (!permission.granted) {
    return (
      <SafeAreaView style={styles.permissionContainer}>
        <Text style={styles.permissionText}>
          Bilet taramak için kamera izni gerekli
        </Text>
        <TouchableOpacity style={styles.permissionButton} onPress={requestPermission}>
          <Text style={styles.permissionButtonText}>İzin Ver</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  if (lastScanResult) {
    return (
      <SafeAreaView style={[
        styles.resultContainer, 
        lastScanResult.isValid ? styles.successContainer : styles.errorContainer
      ]}>
        <View style={styles.resultContent}>
          <Text style={styles.resultIcon}>
            {lastScanResult.isValid ? '✅' : '❌'}
          </Text>
          <Text style={styles.resultTitle}>
            {lastScanResult.isValid ? 'KABUL EDİLDİ' : 'REDDEDİLDİ'}
          </Text>
          <Text style={styles.resultMessage}>
            {lastScanResult.message}
          </Text>
          {lastScanResult.ticket && (
            <View style={styles.ticketInfo}>
              <Text style={styles.ticketName}>{lastScanResult.ticket.holder_name}</Text>
              <Text style={styles.ticketCategory}>
                {lastScanResult.ticket.category.toUpperCase()}
              </Text>
            </View>
          )}
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Bilet Tarayıcı</Text>
        <View style={styles.statusIndicator}>
          {isOnline ? (
            <View style={styles.onlineStatus}>
              <Wifi size={16} color="#10B981" />
              <Text style={styles.statusText}>Çevrimiçi</Text>
            </View>
          ) : (
            <View style={styles.offlineStatus}>
              <WifiOff size={16} color="#F59E0B" />
              <Text style={styles.statusTextOffline}>Çevrimdışı</Text>
            </View>
          )}
        </View>
      </View>

      <View style={styles.cameraContainer}>
        <CameraView
          style={styles.camera}
          facing="back"
          barcodeScannerSettings={{
            barcodeTypes: ['qr', 'ean13', 'ean8', 'code128', 'code39'],
          }}
          onBarcodeScanned={scanning ? handleBarcodeScanned : undefined}
        >
          <View style={styles.overlay}>
            <View style={styles.scanFrame} />
            <Text style={styles.scanInstruction}>
              Kamerayı QR kod veya barkoda yöneltin
            </Text>
          </View>
        </CameraView>
      </View>

      <View style={styles.controls}>
        <TouchableOpacity 
          style={styles.controlButton}
          onPress={() => setScanning(!scanning)}
        >
          <QrCode size={24} color="#FFFFFF" />
          <Text style={styles.controlButtonText}>
            {scanning ? 'Taramayı Duraklat' : 'Taramaya Devam Et'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#F8FAFC',
  },
  permissionText: {
    fontSize: 18,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    marginBottom: 24,
    color: '#374151',
  },
  permissionButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#1F2937',
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  onlineStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#065F46',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    gap: 4,
  },
  offlineStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#92400E',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    gap: 4,
  },
  statusText: {
    color: '#10B981',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  statusTextOffline: {
    color: '#F59E0B',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  cameraContainer: {
    flex: 1,
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  scanFrame: {
    width: 250,
    height: 250,
    borderWidth: 3,
    borderColor: '#3B82F6',
    borderRadius: 16,
    backgroundColor: 'transparent',
  },
  scanInstruction: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginTop: 24,
    textAlign: 'center',
    textShadowColor: 'rgba(0,0,0,0.75)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  controls: {
    padding: 16,
  },
  controlButton: {
    backgroundColor: '#3B82F6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    gap: 8,
  },
  controlButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
  },
  resultContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  successContainer: {
    backgroundColor: '#065F46',
  },
  errorContainer: {
    backgroundColor: '#7F1D1D',
  },
  resultContent: {
    alignItems: 'center',
    padding: 32,
  },
  resultIcon: {
    fontSize: 80,
    marginBottom: 16,
  },
  resultTitle: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  resultMessage: {
    fontSize: 18,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 16,
  },
  ticketInfo: {
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.1)',
    padding: 16,
    borderRadius: 8,
  },
  ticketName: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  ticketCategory: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    opacity: 0.8,
  },
});