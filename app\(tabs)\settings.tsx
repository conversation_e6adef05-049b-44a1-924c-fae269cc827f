import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, Alert } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { TicketService } from '@/lib/ticketService';
import { router } from 'expo-router';
import { LogOut, Download, FolderSync as Sync, User, Shield } from 'lucide-react-native';

export default function SettingsScreen() {
  const { user, userRole, signOut } = useAuth();

  const handleSignOut = async () => {
    Alert.alert(
      'Çıkış Yap',
      'Çık<PERSON><PERSON> yapmak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Çıkış Yap', 
          style: 'destructive',
          onPress: async () => {
            await signOut();
            router.replace('/(auth)/login');
          }
        },
      ]
    );
  };

  const downloadOfflineData = async () => {
    Alert.alert(
      'Offline Veri İndir',
      'Offline tarama için bilet verilerini indir?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'İndir', 
          onPress: async () => {
            const success = await TicketService.downloadTicketsForEvent('event-1');
            Alert.alert(
              success ? 'Başarılı' : 'Hata',
              success ? 'Offline veriler başarıyla indirildi' : 'Offline veri indirme başarısız'
            );
          }
        },
      ]
    );
  };

  const syncOfflineData = async () => {
    Alert.alert(
      'Offline Veri Senkronize Et',
      'Offline taramaları sunucuya yükle?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Senkronize Et', 
          onPress: async () => {
            await TicketService.syncOfflineScans();
            Alert.alert('Başarılı', 'Offline veriler başarıyla senkronize edildi');
          }
        },
      ]
    );
  };

  const SettingRow = ({ 
    icon, 
    title, 
    subtitle, 
    onPress, 
    color = '#3B82F6',
    dangerous = false 
  }: {
    icon: React.ReactNode;
    title: string;
    subtitle?: string;
    onPress: () => void;
    color?: string;
    dangerous?: boolean;
  }) => (
    <TouchableOpacity style={styles.settingRow} onPress={onPress}>
      <View style={[styles.settingIcon, { backgroundColor: color + '20' }]}>
        {icon}
      </View>
      <View style={styles.settingContent}>
        <Text style={[styles.settingTitle, dangerous && { color: '#EF4444' }]}>
          {title}
        </Text>
        {subtitle && (
          <Text style={styles.settingSubtitle}>{subtitle}</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Ayarlar</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* User Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Hesap</Text>
          <View style={styles.userCard}>
            <View style={styles.userInfo}>
              <View style={styles.userAvatar}>
                <User size={24} color="#3B82F6" />
              </View>
              <View>
                <Text style={styles.userName}>{user?.email}</Text>
                <View style={styles.roleContainer}>
                  <Shield size={14} color="#6B7280" />
                  <Text style={styles.userRole}>
                    {userRole === 'admin' ? 'Yönetici' : 'Tarayıcı'}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* Sync & Offline */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Veri Yönetimi</Text>
          <SettingRow
            icon={<Download size={20} color="#10B981" />}
            title="Offline Veri İndir"
            subtitle="Offline tarama için biletleri indir"
            onPress={downloadOfflineData}
            color="#10B981"
          />
          <SettingRow
            icon={<Sync size={20} color="#3B82F6" />}
            title="Offline Taramaları Senkronize Et"
            subtitle="Offline taramaları sunucuya yükle"
            onPress={syncOfflineData}
            color="#3B82F6"
          />
        </View>

        {/* Account Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Hesap</Text>
          <SettingRow
            icon={<LogOut size={20} color="#EF4444" />}
            title="Çıkış Yap"
            subtitle="Hesabınızdan çıkış yapın"
            onPress={handleSignOut}
            color="#EF4444"
            dangerous
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginBottom: 12,
  },
  userCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#EBF4FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  userName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 4,
  },
  roleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  userRole: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  settingRow: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  settingSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
});