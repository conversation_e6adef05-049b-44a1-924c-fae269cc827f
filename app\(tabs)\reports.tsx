import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, SafeAreaView } from 'react-native';
import { TicketService } from '@/lib/ticketService';
import { EventStats } from '@/lib/mockData';
import { ChartBar as BarChart3, TrendingUp, Users, TriangleAlert as Alert<PERSON>riangle, Crown } from 'lucide-react-native';

export default function ReportsScreen() {
  const [stats, setStats] = useState<EventStats | null>(null);
  const [selectedEventId, setSelectedEventId] = useState('demo-event-1');
  const [selectedEventId, setSelectedEventId] = useState('event-1');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, [selectedEventId]);

  const loadStats = async () => {
    setLoading(true);
    const eventStats = await TicketService.getEventStats(selectedEventId);
    setStats(eventStats);
    setLoading(false);
  };

  const StatCard = ({ 
    title, 
    value, 
    icon, 
    color = '#3B82F6',
    subtitle 
  }: { 
    title: string; 
    value: string | number; 
    icon: React.ReactNode; 
    color?: string;
    subtitle?: string;
  }) => (
    <View style={[styles.statCard, { borderLeftColor: color }]}>
      <View style={styles.statHeader}>
        <View style={[styles.iconContainer, { backgroundColor: color + '20' }]}>
          {icon}
        </View>
        <View style={styles.statContent}>
          <Text style={styles.statValue}>{value}</Text>
          <Text style={styles.statTitle}>{title}</Text>
          {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
        </View>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>İstatistikler yükleniyor...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Etkinlik Raporları</Text>
        <TouchableOpacity style={styles.refreshButton} onPress={loadStats}>
          <Text style={styles.refreshButtonText}>Yenile</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {stats && (
          <>
            <View style={styles.overviewSection}>
              <Text style={styles.sectionTitle}>Genel Bakış</Text>
              <View style={styles.statsGrid}>
                <StatCard
                  title="Toplam Bilet"
                  value={stats.total_tickets}
                  icon={<Users size={24} color="#3B82F6" />}
                  color="#3B82F6"
                />
                
                <StatCard
                  title="Taranan"
                  value={stats.scanned_tickets}
                  icon={<BarChart3 size={24} color="#10B981" />}
                  color="#10B981"
                  subtitle={`%${((stats.scanned_tickets / stats.total_tickets) * 100).toFixed(1)} tarandı`}
                />
                
                <StatCard
                  title="Bekleyen"
                  value={stats.pending_tickets}
                  icon={<TrendingUp size={24} color="#F59E0B" />}
                  color="#F59E0B"
                  subtitle="Henüz taranmadı"
                />
                
                <StatCard
                  title="Çift Giriş Denemesi"
                  value={stats.duplicate_attempts}
                  icon={<AlertTriangle size={24} color="#EF4444" />}
                  color="#EF4444"
                  subtitle="Güvenlik uyarıları"
                />
              </View>
            </View>

            <View style={styles.categorySection}>
              <Text style={styles.sectionTitle}>Kategoriye Göre</Text>
              <View style={styles.categoryGrid}>
                <StatCard
                  title="VIP Taranan"
                  value={stats.vip_scanned}
                  icon={<Crown size={24} color="#8B5CF6" />}
                  color="#8B5CF6"
                />
                
                <StatCard
                  title="Normal Taranan"
                  value={stats.regular_scanned}
                  icon={<Users size={24} color="#06B6D4" />}
                  color="#06B6D4"
                />
              </View>
            </View>

            <View style={styles.progressSection}>
              <Text style={styles.sectionTitle}>İlerleme</Text>
              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <View 
                    style={[
                      styles.progressFill, 
                      { 
                        width: `${(stats.scanned_tickets / stats.total_tickets) * 100}%`,
                        backgroundColor: '#10B981'
                      }
                    ]} 
                  />
                </View>
                <Text style={styles.progressText}>
                  {stats.total_tickets} biletten {stats.scanned_tickets} tanesi tarandı
                </Text>
              </View>
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
  },
  refreshButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  refreshButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  overviewSection: {
    marginBottom: 24,
  },
  categorySection: {
    marginBottom: 24,
  },
  progressSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  statsGrid: {
    gap: 12,
  },
  categoryGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    flex: 1,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  statContent: {
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
  },
  statTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
    marginTop: 2,
  },
  statSubtitle: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    marginTop: 2,
  },
  progressContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 12,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
});