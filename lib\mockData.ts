export interface User {
  id: string;
  email: string;
  role: 'admin' | 'scanner';
  name: string;
  created_at: string;
}

export interface Event {
  id: string;
  name: string;
  description?: string;
  start_date: string;
  end_date: string;
  venue: string;
  created_by: string;
  is_active: boolean;
  created_at: string;
}

export interface Ticket {
  id: string;
  ticket_code: string;
  event_id: string;
  category: 'regular' | 'vip' | 'backstage' | 'press';
  holder_name: string;
  holder_email: string;
  is_used: boolean;
  used_at?: string;
  used_by?: string;
  created_at: string;
}

export interface Scan {
  id: string;
  ticket_id: string;
  scanned_by: string;
  scanned_at: string;
  is_duplicate: boolean;
  event_id: string;
}

export interface EventStats {
  total_tickets: number;
  scanned_tickets: number;
  pending_tickets: number;
  duplicate_attempts: number;
  vip_scanned: number;
  regular_scanned: number;
  backstage_scanned: number;
  press_scanned: number;
}

// Mock Users
export const mockUsers: User[] = [
  {
    id: 'admin-1',
    email: '<EMAIL>',
    role: 'admin',
    name: 'Admin User',
    created_at: '2025-01-01T00:00:00Z'
  },
  {
    id: 'scanner-1',
    email: '<EMAIL>',
    role: 'scanner',
    name: '<PERSON><PERSON>r User',
    created_at: '2025-01-01T00:00:00Z'
  }
];

// Mock Events
export const mockEvents: Event[] = [
  {
    id: 'event-1',
    name: 'Yaz Müzik Festivali 2025',
    description: 'Çok sahneli yıllık yaz müzik festivali',
    start_date: '2025-07-15T18:00:00Z',
    end_date: '2025-07-17T02:00:00Z',
    venue: 'Merkez Park Amfiteatr',
    created_by: 'admin-1',
    is_active: true,
    created_at: '2025-01-01T00:00:00Z'
  },
  {
    id: 'event-2',
    name: 'Teknoloji Konferansı 2025',
    description: 'Yıllık teknoloji ve inovasyon konferansı',
    start_date: '2025-09-10T09:00:00Z',
    end_date: '2025-09-12T18:00:00Z',
    venue: 'Kongre Merkezi',
    created_by: 'admin-1',
    is_active: true,
    created_at: '2025-01-02T00:00:00Z'
  }
];

// Mock Tickets
export const mockTickets: Ticket[] = [
  {
    id: 'ticket-1',
    ticket_code: 'FESTIVAL2025001',
    event_id: 'event-1',
    category: 'regular',
    holder_name: 'Ahmet Yılmaz',
    holder_email: '<EMAIL>',
    is_used: false,
    created_at: '2025-01-01T00:00:00Z'
  },
  {
    id: 'ticket-2',
    ticket_code: 'FESTIVAL2025002',
    event_id: 'event-1',
    category: 'vip',
    holder_name: 'Ayşe Demir',
    holder_email: '<EMAIL>',
    is_used: true,
    used_at: '2025-01-15T14:30:00Z',
    used_by: 'scanner-1',
    created_at: '2025-01-01T00:00:00Z'
  },
  {
    id: 'ticket-3',
    ticket_code: 'FESTIVAL2025003',
    event_id: 'event-1',
    category: 'backstage',
    holder_name: 'Mehmet Kaya',
    holder_email: '<EMAIL>',
    is_used: false,
    created_at: '2025-01-01T00:00:00Z'
  },
  {
    id: 'ticket-4',
    ticket_code: 'FESTIVAL2025004',
    event_id: 'event-1',
    category: 'press',
    holder_name: 'Fatma Özkan',
    holder_email: '<EMAIL>',
    is_used: false,
    created_at: '2025-01-01T00:00:00Z'
  },
  {
    id: 'ticket-5',
    ticket_code: 'FESTIVAL2025005',
    event_id: 'event-1',
    category: 'regular',
    holder_name: 'Ali Çelik',
    holder_email: '<EMAIL>',
    is_used: true,
    used_at: '2025-01-15T15:45:00Z',
    used_by: 'scanner-1',
    created_at: '2025-01-01T00:00:00Z'
  },
  {
    id: 'ticket-6',
    ticket_code: 'TECH2025001',
    event_id: 'event-2',
    category: 'regular',
    holder_name: 'Zeynep Arslan',
    holder_email: '<EMAIL>',
    is_used: false,
    created_at: '2025-01-02T00:00:00Z'
  }
];

// Mock Scans
export const mockScans: Scan[] = [
  {
    id: 'scan-1',
    ticket_id: 'FESTIVAL2025002',
    scanned_by: 'scanner-1',
    scanned_at: '2025-01-15T14:30:00Z',
    is_duplicate: false,
    event_id: 'event-1'
  },
  {
    id: 'scan-2',
    ticket_id: 'FESTIVAL2025005',
    scanned_by: 'scanner-1',
    scanned_at: '2025-01-15T15:45:00Z',
    is_duplicate: false,
    event_id: 'event-1'
  },
  {
    id: 'scan-3',
    ticket_id: 'FESTIVAL2025002',
    scanned_by: 'scanner-1',
    scanned_at: '2025-01-15T16:20:00Z',
    is_duplicate: true,
    event_id: 'event-1'
  }
];